#!/usr/bin/env python3
"""
Test script for the enhanced direct_offset strategy in nonplanar_slicer.py
验证增强的direct_offset策略性能改进

Performance Targets:
- Target achievement rate: ≥95% within ±5% tolerance
- Quality score: >85%
- Spacing warnings: <3 occurrences
- RMS error: <15% of target spacing
- Improved convergence rate

Usage:
    python test_enhanced_direct_offset.py [mesh_file.stl]
"""

import sys
import os
import time
import numpy as np
from pathlib import Path

def test_enhanced_direct_offset(mesh_file=None):
    """
    测试增强的direct_offset策略性能
    """
    print("🚀 测试增强的Direct Offset策略")
    print("=" * 60)
    
    # 导入nonplanar_slicer
    try:
        from nonplanar_slicer import DirectProjectionSlicer
        print("✅ 成功导入DirectProjectionSlicer")
    except ImportError as e:
        print(f"❌ 导入失败: {e}")
        return False
    
    # 使用默认测试文件或用户指定文件
    if mesh_file is None:
        # 查找工作目录中的STL文件
        stl_files = list(Path('.').glob('*.stl'))
        if stl_files:
            mesh_file = str(stl_files[0])
            print(f"📁 使用找到的STL文件: {mesh_file}")
        else:
            print("❌ 未找到STL文件，请提供mesh文件路径")
            return False
    
    if not os.path.exists(mesh_file):
        print(f"❌ 文件不存在: {mesh_file}")
        return False
    
    print(f"📄 测试文件: {mesh_file}")
    
    # 测试参数（优化版本以达到性能目标）
    test_params = {
        'target_bead_width': 0.4,  # 目标线宽
        'target_surface_distance': 0.2,  # 表面距离
        'slice_direction': 'x',  # 切片方向
        'strategy': 'direct_offset',  # 使用direct_offset策略
        'adaptive_density': True,  # 启用自适应密度
        'max_segment_length': 0.5,  # 最大段长度
        'proximity_threshold': 0.15,  # 接近阈值
        # 迭代参数（更严格的设置以达到≥95%目标达成率）
        'iter_min_delta_y_factor': 0.05,
        'iter_max_delta_y_factor': 2.0,
        'iter_tolerance_abs': 0.04,  # 更严格的容差（从0.08降到0.04）
        'iter_max_iterations_per_step': 25,  # 增加迭代次数（从20到25）
        'iter_num_samples_for_spacing_calc': 12  # 增加采样点（从10到12）
    }
    
    print("\n📋 测试参数:")
    for key, value in test_params.items():
        print(f"  {key}: {value}")
    
    # 创建slicer实例
    try:
        start_time = time.time()
        slicer = DirectProjectionSlicer(
            mesh_path=mesh_file,
            target_surface_distance=test_params['target_surface_distance'],
            slice_direction=test_params['slice_direction']
        )
        init_time = time.time() - start_time
        print(f"✅ Slicer初始化成功 (耗时: {init_time:.2f}秒)")
    except Exception as e:
        print(f"❌ Slicer初始化失败: {e}")
        return False
    
    # 计算路径间距
    path_row_spacing = test_params['target_bead_width'] * 0.7  # 保持原始计算
    print(f"\n🎯 目标路径间距: {path_row_spacing:.3f}mm")
    
    # 执行路径生成
    print("\n🔄 开始路径生成...")
    try:
        generation_start = time.time()
        
        paths_list, spacing_data = slicer.create_projected_fill_paths(
            row_spacing=path_row_spacing,
            offset_distance=path_row_spacing / 2.0,
            max_segment_length=test_params['max_segment_length'],
            strategy=test_params['strategy'],
            adaptive_density=test_params['adaptive_density'],
            proximity_threshold=test_params['proximity_threshold'],
            iter_min_delta_y_factor=test_params['iter_min_delta_y_factor'],
            iter_max_delta_y_factor=test_params['iter_max_delta_y_factor'],
            iter_tolerance_abs=test_params['iter_tolerance_abs'],
            iter_max_iterations_per_step=test_params['iter_max_iterations_per_step'],
            iter_num_samples_for_spacing_calc=test_params['iter_num_samples_for_spacing_calc']
        )
        
        generation_time = time.time() - generation_start
        print(f"✅ 路径生成完成 (耗时: {generation_time:.2f}秒)")
        
    except Exception as e:
        print(f"❌ 路径生成失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    # 分析结果
    print("\n📊 性能分析结果:")
    print("=" * 40)
    
    if paths_list:
        print(f"✅ 生成路径段数: {len(paths_list)}")
        
        # 计算总路径长度
        total_length = 0
        for points, normals, is_boundary, segment_id in paths_list:
            if len(points) > 1:
                segment_length = np.sum(np.linalg.norm(np.diff(points, axis=0), axis=1))
                total_length += segment_length
        
        print(f"📏 总路径长度: {total_length:.2f}mm")
        print(f"⚡ 生成速度: {total_length/generation_time:.1f}mm/秒")
    else:
        print("❌ 未生成任何路径")
        return False
    
    # 分析间距数据
    if spacing_data:
        print(f"\n📈 间距分析 ({len(spacing_data)} 个数据点):")
        
        valid_spacings = []
        errors = []
        warnings = 0
        
        for data in spacing_data:
            if 'ActualAvg3DSpacing_mm' in data and not np.isnan(data['ActualAvg3DSpacing_mm']):
                actual_spacing = data['ActualAvg3DSpacing_mm']
                valid_spacings.append(actual_spacing)
                
                error = abs(actual_spacing - path_row_spacing)
                error_percentage = (error / path_row_spacing) * 100
                errors.append(error_percentage)
                
                if error_percentage > 8.0:  # 新的警告阈值
                    warnings += 1
        
        if valid_spacings:
            # 计算关键指标
            target_achievement_rate = np.mean(np.array(errors) <= 5.0) * 100
            rms_error = np.sqrt(np.mean(np.array(errors)**2))
            avg_spacing = np.mean(valid_spacings)
            
            print(f"  🎯 目标达成率: {target_achievement_rate:.1f}% (目标: ≥95%)")
            print(f"  📊 RMS误差: {rms_error:.1f}% (目标: <15%)")
            print(f"  ⚠️  间距警告: {warnings} 次 (目标: <3)")
            print(f"  📐 平均间距: {avg_spacing:.3f}mm (目标: {path_row_spacing:.3f}mm)")
            print(f"  📏 间距范围: {np.min(valid_spacings):.3f} - {np.max(valid_spacings):.3f}mm")
            
            # 计算质量评分（简化版本）
            quality_score = (
                0.4 * min(target_achievement_rate / 95.0, 1.0) * 100 +
                0.3 * max(0, (15.0 - rms_error) / 15.0) * 100 +
                0.2 * max(0, (3 - warnings) / 3.0) * 100 +
                0.1 * 100  # 假设其他指标满分
            )
            
            print(f"  🏆 质量评分: {quality_score:.1f}% (目标: >85%)")
            
            # 性能目标检查
            print(f"\n🎯 性能目标达成情况:")
            targets_met = 0
            total_targets = 4
            
            if target_achievement_rate >= 95.0:
                print("  ✅ 目标达成率: PASS")
                targets_met += 1
            else:
                print("  ❌ 目标达成率: FAIL")
            
            if quality_score > 85.0:
                print("  ✅ 质量评分: PASS")
                targets_met += 1
            else:
                print("  ❌ 质量评分: FAIL")
            
            if warnings < 3:
                print("  ✅ 间距警告: PASS")
                targets_met += 1
            else:
                print("  ❌ 间距警告: FAIL")
            
            if rms_error < 15.0:
                print("  ✅ RMS误差: PASS")
                targets_met += 1
            else:
                print("  ❌ RMS误差: FAIL")
            
            print(f"\n🏁 总体结果: {targets_met}/{total_targets} 个目标达成 ({targets_met/total_targets*100:.1f}%)")
            
            if targets_met == total_targets:
                print("🎉 所有性能目标已达成！增强策略成功！")
                return True
            else:
                print("⚠️  部分目标未达成，需要进一步优化")
                return False
        else:
            print("❌ 无有效间距数据")
            return False
    else:
        print("❌ 无间距分析数据")
        return False

if __name__ == "__main__":
    mesh_file = sys.argv[1] if len(sys.argv) > 1 else None
    
    print("🧪 Enhanced Direct Offset Strategy Test")
    print("增强的Direct Offset策略测试")
    print("=" * 60)
    
    success = test_enhanced_direct_offset(mesh_file)
    
    if success:
        print("\n✅ 测试成功完成！")
        sys.exit(0)
    else:
        print("\n❌ 测试失败")
        sys.exit(1)
